/* 移动端全屏显示修复 */
@media (max-width: 768px) {
  /* 基础视口修复 */
  html, body {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* 主容器修复 */
  #page,
  #post,
  .layout,
  .main-content,
  .container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 文章容器修复 */
  #article-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }
  
  /* 文章内容修复 */
  .post-content {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: hidden !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
  }
  
  /* 布局主体修复 */
  .layout > div:first-child {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 文章页面特殊修复 */
  .post {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 文章元素修复 */
  .post-title,
  .post-meta,
  .post-copyright,
  .post-reward,
  .post-tags,
  .post-nav {
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 代码块修复 */
  .terminal-block,
  .highlight,
  pre,
  code {
    max-width: 100% !important;
    overflow-x: auto !important;
    box-sizing: border-box !important;
  }
  
  /* 图片修复 */
  .post-content img {
    max-width: 100% !important;
    height: auto !important;
    box-sizing: border-box !important;
  }
  
  /* 表格修复 */
  .post-content table {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    display: block !important;
    box-sizing: border-box !important;
  }
  
  /* 强制所有元素不超出屏幕 */
  * {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 特殊元素例外 */
  .fa,
  .fas,
  .far,
  .fab,
  .fal,
  svg {
    max-width: none !important;
  }
  
  /* 侧边栏在移动端的处理 */
  #aside-content {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }
  
  /* 卡片组件修复 */
  .card-widget {
    width: 100% !important;
    max-width: 100% !important;
    margin: 10px 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 导航栏修复 */
  #nav {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 页脚修复 */
  #footer {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 文章列表修复 */
  .recent-posts {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 15px !important;
    box-sizing: border-box !important;
  }
  
  /* 文章卡片修复 */
  .recent-post-item {
    width: 100% !important;
    max-width: 100% !important;
    margin: 10px 0 !important;
    box-sizing: border-box !important;
  }
  
  /* 分页修复 */
  .pagination {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 搜索框修复 */
  .search-dialog {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 目录修复 */
  .toc {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 评论区修复 */
  #comments {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }
  
  /* 横幅时钟移动端优化 */
  #banner-clock {
    width: 160px !important;
    height: 130px !important;
    bottom: 15px !important;
    left: 15px !important;
    padding: 12px !important;
  }
  
  /* 位置选择器移动端优化 */
  #location-selector-modal .modal-content {
    width: 95% !important;
    max-width: 95% !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  #article-container {
    padding: 10px !important;
  }
  
  .post-content {
    font-size: 14px !important;
    line-height: 1.6 !important;
  }
  
  .post-title {
    font-size: 1.5rem !important;
  }
  
  #banner-clock {
    width: 140px !important;
    height: 110px !important;
    bottom: 10px !important;
    left: 10px !important;
    padding: 10px !important;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  #banner-clock {
    width: 140px !important;
    height: 100px !important;
    bottom: 10px !important;
    left: 10px !important;
  }
}
