<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>移动端显示测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .test-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-error { background: #F44336; }
        
        .viewport-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .viewport-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
        }
        
        .info-item {
            background: white;
            padding: 8px;
            border-radius: 4px;
        }
        
        .test-links {
            margin-top: 30px;
        }
        
        .test-link {
            display: block;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
            text-align: center;
            transition: background 0.3s;
        }
        
        .test-link:hover {
            background: #45a049;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .test-container {
                padding: 15px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📱 移动端显示测试</h1>
            <p>检测移动端页面显示是否正常</p>
        </div>
        
        <div class="viewport-info">
            <h3>📊 设备信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <strong>屏幕宽度:</strong> <span id="screen-width">-</span>px
                </div>
                <div class="info-item">
                    <strong>视口宽度:</strong> <span id="viewport-width">-</span>px
                </div>
                <div class="info-item">
                    <strong>设备像素比:</strong> <span id="device-ratio">-</span>
                </div>
                <div class="info-item">
                    <strong>用户代理:</strong> <span id="user-agent">-</span>
                </div>
            </div>
        </div>
        
        <div class="test-item">
            <div class="test-title">
                <span class="status-indicator" id="viewport-status"></span>
                视口配置检测
            </div>
            <div class="test-description" id="viewport-desc">
                检测页面是否正确设置了移动端视口...
            </div>
        </div>
        
        <div class="test-item">
            <div class="test-title">
                <span class="status-indicator" id="width-status"></span>
                页面宽度检测
            </div>
            <div class="test-description" id="width-desc">
                检测页面是否能够正确适应移动端屏幕宽度...
            </div>
        </div>
        
        <div class="test-item">
            <div class="test-title">
                <span class="status-indicator" id="overflow-status"></span>
                横向滚动检测
            </div>
            <div class="test-description" id="overflow-desc">
                检测页面是否存在不必要的横向滚动...
            </div>
        </div>
        
        <div class="test-item">
            <div class="test-title">
                <span class="status-indicator" id="css-status"></span>
                CSS修复检测
            </div>
            <div class="test-description" id="css-desc">
                检测移动端CSS修复是否已加载...
            </div>
        </div>
        
        <div class="test-links">
            <h3>🔗 测试链接</h3>
            <a href="http://localhost:4000" class="test-link">访问主站首页</a>
            <a href="http://localhost:4000/2025/06/08/emby影视/" class="test-link">测试文章页面</a>
            <a href="http://localhost:4000/archives/" class="test-link">测试归档页面</a>
        </div>
    </div>
    
    <script>
        // 获取设备信息
        function updateDeviceInfo() {
            document.getElementById('screen-width').textContent = screen.width;
            document.getElementById('viewport-width').textContent = window.innerWidth;
            document.getElementById('device-ratio').textContent = window.devicePixelRatio || 1;
            document.getElementById('user-agent').textContent = navigator.userAgent.length > 50 ? 
                navigator.userAgent.substring(0, 50) + '...' : navigator.userAgent;
        }
        
        // 检测视口配置
        function checkViewport() {
            const viewport = document.querySelector('meta[name="viewport"]');
            const status = document.getElementById('viewport-status');
            const desc = document.getElementById('viewport-desc');
            
            if (viewport && viewport.content.includes('width=device-width')) {
                status.className = 'status-indicator status-good';
                desc.textContent = '✅ 视口配置正确，已设置 width=device-width';
            } else {
                status.className = 'status-indicator status-error';
                desc.textContent = '❌ 视口配置缺失或不正确';
            }
        }
        
        // 检测页面宽度
        function checkPageWidth() {
            const status = document.getElementById('width-status');
            const desc = document.getElementById('width-desc');
            const bodyWidth = document.body.scrollWidth;
            const viewportWidth = window.innerWidth;
            
            if (bodyWidth <= viewportWidth + 10) { // 允许10px误差
                status.className = 'status-indicator status-good';
                desc.textContent = `✅ 页面宽度正常 (${bodyWidth}px ≤ ${viewportWidth}px)`;
            } else {
                status.className = 'status-indicator status-warning';
                desc.textContent = `⚠️ 页面可能过宽 (${bodyWidth}px > ${viewportWidth}px)`;
            }
        }
        
        // 检测横向滚动
        function checkHorizontalScroll() {
            const status = document.getElementById('overflow-status');
            const desc = document.getElementById('overflow-desc');
            const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
            
            if (!hasHorizontalScroll) {
                status.className = 'status-indicator status-good';
                desc.textContent = '✅ 无横向滚动，页面适配良好';
            } else {
                status.className = 'status-indicator status-warning';
                desc.textContent = '⚠️ 检测到横向滚动，可能影响移动端体验';
            }
        }
        
        // 检测CSS修复
        function checkCSSFix() {
            const status = document.getElementById('css-status');
            const desc = document.getElementById('css-desc');
            
            // 检测是否有mobile-fix.css的样式
            const testElement = document.createElement('div');
            testElement.style.cssText = 'position: absolute; top: -9999px; left: -9999px;';
            testElement.className = 'mobile-fix-test';
            document.body.appendChild(testElement);
            
            // 简单检测：如果页面宽度控制正常，认为CSS修复生效
            const bodyWidth = document.body.scrollWidth;
            const viewportWidth = window.innerWidth;
            
            document.body.removeChild(testElement);
            
            if (bodyWidth <= viewportWidth + 10) {
                status.className = 'status-indicator status-good';
                desc.textContent = '✅ 移动端CSS修复已生效';
            } else {
                status.className = 'status-indicator status-warning';
                desc.textContent = '⚠️ 移动端CSS修复可能未完全生效';
            }
        }
        
        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            checkViewport();
            checkPageWidth();
            checkHorizontalScroll();
            checkCSSFix();
            
            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                updateDeviceInfo();
                checkPageWidth();
                checkHorizontalScroll();
            });
        });
        
        // 每秒更新一次检测（用于动态内容）
        setInterval(function() {
            checkPageWidth();
            checkHorizontalScroll();
        }, 1000);
    </script>
</body>
</html>
