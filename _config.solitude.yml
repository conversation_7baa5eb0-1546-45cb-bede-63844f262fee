# ---------------------------
# Hexo Theme Solitude 主题配置文件
# 作者: 伍十七(@everfu)
# Github: https://github.com/everfu/hexo-theme-solitude
#
# 指南: https://solitude.js.org/zh
# 你可以从指南中获取更详细的帮助
#
# 赞助：https://afdian.com/a/everfu
# ---------------------------

### 基础配置

# --------------------------- start ---------------------------
# 网站信息
site:
  name:
    class: text # text / i_class / img
    custom: 主页 # Solitude / fas fa-ghost / /img/pwa/favicon.ico
  icon: /img/photo.jpg # 网站图标
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 导航栏
nav:
  # 左侧盒子
  group:
  #  project: # 项目名称
  #    Solitude: https://github.com/everfu/hexo-theme-solitude || /img/pwa/favicon.ico # 名称: 链接 || 图标

  # 菜单
  menu:
    首页: / # 名称: 链接
    文库: # 名称
      全部文章: /archives/ || fas fa-folder-closed # 项目名称: 链接 || 图标
      全部分类: /categories/ || fas fa-clone
      全部标签: /tags/ || fas fa-tags
    友链: # 名称
      友情链接: https://github.com/qianxiu203 || fas fa-user-group
      朋友圈: https://t.me/qianxiua || fas fa-wifi
    关于: # 名称
      关于我: https://github.com/qianxiu203 || fas fa-user
      在线工具: /tools/ || fas fa-toolbox

  # 右侧按钮
  right:
    random: true # 随机文章按钮
    custom:
      - name: GitHub # 按钮名字
        url: https://github.com/qianxiu203 # 跳转链接
        icon: fab fa-github # 图标
        onclick: # 点击事件
        id: github_button # 元素ID

      - name: 主题切换 # 按钮名字
        url: javascript:void(0) # 跳转链接
        icon: fas fa-circle-half-stroke # 图标
        onclick: sco.switchDarkMode() # 点击事件
        id: theme_switch_button # 元素ID
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 首页顶部横幅
hometop:
  enable: true
  banner:
    title: 分享技术<br >与科技生活 # 大标题
    desc:
      一个热爱生活的人 # 小标题
      # - 我只是一个普通的程序员
      # - 但我有一个不平凡的梦想
      # - 我希望能够改变世界
    icon:
      HTML: # 名称
        img: https://i.postimg.cc/vBWVnY8q/html.png # 图片链接
        color: "#e9572b" # 颜色
      JS:
        img: https://i.postimg.cc/3N10Ltv2/js.png
        color: "#f7cb4f"
      Docker:
        img: https://i.postimg.cc/8Pk6Fg24/docker.png
        color: "#57b6e6"
      Flutter:
        img: https://i.postimg.cc/hPC7T3gB/flutter.png
        color: "#ffffff"
      WebPack:
        img: https://i.postimg.cc/dVLZBmtT/webpack.png
        color: "#2e3a41"
      Git:
        img: https://i.postimg.cc/nhgjwjCS/git.png
        color: "#df5b40"
  group:
    # 热门: /tags/Fire/ || fas fa-fire || linear-gradient(to right,#f65,#ffbf37)
  recommendList:
    enable: true
    sup: 置顶
    title: Solitude 官方文档
    url: https://solitude.js.org/
    img: /img/default.avif
    color: "none"

# 文章推荐轮播图
carousel: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 侧边栏
aside:
  # 值: about(信息卡), newestPost(最新文章), allInfo(网站信息), newest_comment(最新评论), clock(时钟组件)
  # Sticky: 固定位置 / noSticky: 不固定位置
  home: # 首页
    noSticky: "about,clock"
    Sticky: "allInfo"
  post: # 文章页面
    noSticky: "about,clock"
    Sticky: "newestPost"
  page: # 页面
    noSticky: "about,clock"
    Sticky: "newestPost,allInfo"
  # 菜单栏位置(0: 左 1: 右)
  position: 0 # 侧边栏定位(0: 左 1: 右)

  # --------------------------- start ---------------------------
  # 信息卡
  my_card:
    author:
      img: /img/photo.jpg # 头像链接
      sticker: # 贴纸链接，24x24 尺寸
    # 介绍
    description: 只有迎风，风筝才能飞得更高。
    # 内容
    content: # 这是我的博客
    state:
      morning: ✨ 早上好，新的一天开始了
      noon: 🍲 午餐时间
      afternoon: 🌞 下午好
      night: 早点休息
      goodnight: 晚安 😴
    witty_words:
      # - 你可以的
      # - 你一定可以的
      # - 祝你好运，陌生人
    # 社交信息图标
    information:
    #  Github: https://github.com/everfu || fab fa-github # 名称: 链接 || 图标
    #  Bilibili: https://space.bilibili.com/1329819902 || fab fa-bilibili
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # 文章目录
  toc:
    post: true
    page: false
    vague: true
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # 标签
  tags:
    enable: false
    limit: 20 # 显示的标签数量
    # 高亮标签
    highlight_list:
      # - Hexo
  # --------------------------- end ---------------------------

  # --------------------------- start ---------------------------
  # 网站信息
  siteinfo:
    # 文章数量
    postcount: true
    # 总字数
    wordcount: false
    # PV
    pv: true
    # UV
    uv: true
    # 最后更新日期
    updatetime: true
    # 网站创建时间
    runtimeenable: true
    # 格式: yyyy-MM-dd hh-mm-ss
    runtime: "2023-04-20 00:00:00"
  # --------------------------- end ---------------------------
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 页面默认设置
page:
  # 404 页面
  error: true
  # 标签页面
  tags: true
  # 分类页面
  categories: true
  # list: 排序列表 / 1: 跟随首页列表
  archives: 0
  # 默认值
  default:
    # 未设置封面时的默认图片
    cover:
      # - /img/default.png # 默认图片
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 文章默认设置
post:
  default:
    # 未设置封面时的默认图片
    cover:
      # -  # 默认图片
    # 位置
    locate: China
    # 版权
    copyright:
      enable: true
      author: /img/logo.png # 链接
      # 许可证
      license: CC BY-NC-SA 4.0
      # 许可证链接
      licenurl: https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh-hans
  # 文章本地AI
  ai:
    enable: false
    modelName: 小七 GPT
  # 文章元信息
  meta:
    # 发布日期
    date: false
    # 更新日期
    updated: false
    # 位置
    locate: false
    # 字数
    wordcount: false
    # uv
    readtime: false
    # pv
    pv: false
    # 评论数
    comment: false
  # 打赏
  award:
    enable: false
    appreciators: /about/ # 打赏页面
    # 打赏标题
    title: # 感谢您的赞赏
    desc: # 由于您的支持，我才能够实现写作的价值。
    # 打赏列表
    list:
      # - name: Github Sponsor
      #   qcode: https://s3.qjqq.cn/47/661ba900c4bc1.webp!color
      #   url: https://github.com/sponsors/everfu
      #   color: var(--efu-black)

  # 分享图标
  share:
    enable: false
    list:
      # - qq
      # - weibo
      # - twitter
      # - facebook
      # - telegram
      # - whatsapp
      # - linkedin
      # - link
      # - qrcode
  rss: # /atom.xml
  # 文章封面取色
  covercolor:
    enable: false
    # local: 本地取色 / api: api取色 / ave: oss平均色
    mode: local
    # api 地址
    api: https://api.qjqq.cn/api/Imgcolor?img=
    # 缓存时间
    time: 43200000
  footer:
    enable: false
    desc: # 文章来自 Ever Fu # 描述
    button: # 按钮
      enable: true
      name: # 了解更多
      url: /about/
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 自定义主题颜色
theme_color:
  dark: "#ffc848" # 深色模式
  light: "#425AEF" # 浅色模式
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 显示模式
display_mode:
  # auto: 自动切换（识别设备当前主题模式） / dark: 深色模式 / light: 浅色模式
  type: auto
  # 开启后深色模式会显示星空背景
  universe: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 字体
font:
  font-size: 16px
  code-font-size: 16px
  # 全局字体
  font-family: "PingFang SC, Hiragino Sans GB, Microsoft YaHei, sans-serif"
  # 代码字体
  code-font-family: '"monospace", monospace'
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 背景图片
background:
  enable: false
  opacity: .2
  dark: https://i.pinimg.com/originals/d8/b3/9d/d8b39d12b653810db452c437211aeb2e.png
  light: https://i.pinimg.com/originals/93/57/38/935738ed9657b296c2ef0ebd2151eb66.jpg
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 诱导
# 当用户退出页面时，修改标题
lure:
  enable: false
  jump: 404 Not Found
  back: ヾ(≧∇≦*)ゝHey, hey, you fell for it.
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 文章过期
expire:
  enable: false
  time: 30 # 天数
  position: top # top / bottom
  text_prev: "This article expired "
  text_next: " day ago, if the content does not match, please contact the webmaster to update it."
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 首页文章配置信息
index_post_list:
  direction: column # row / column
  column: 2 # 2: 2列 3: 3列
  cover: both
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 相关文章
related_post:
  enable: false
  limit: 2
  # created: 发布日期 / updated: 更新日期
  date_type: created
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 自定义右键菜单
right_menu:
  enable: false
  # 是否显示热门评论开关
  commentBarrage: false
  # 是否在按住 Ctrl 键时显示浏览器右键菜单
  ctrlOriginalMenu: false
  # 简繁体转换
  translate: false
  # 自定义列表
  custom_list:
    # - name: 随机文章
    #   click: toRandomPost()
    #   id: menu-randomPost
    #   class:
    #   icon: fas fa-tower-broadcast
    # - name: 全部分类
    #   click: pjax.loadUrl('/categories/') # 外部链接使用 window.open，pjax 无法请求跨域内容
    #   id:
    #   class:
    #   icon: fas fa-clone
    # - name: 全部标签
    #   click: pjax.loadUrl('/tags/')
    #   id:
    #   class:
    #   icon: fas fa-tags
# --------------------------- end -----------------------

# --------------------------- start ---------------------------
# 复制信息
copy:
  enable: false
  # 复制后链接版权信息
  copyright:
    enable: false
    # 复制文字时超过多少字数显示
    limit: 50
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Mermaid
mermaid: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Chart.js
chart: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# typeit
typeit: false
# --------------------------- end ---------------------------

### 扩展配置

# --------------------------- start ---------------------------
# 控制台
console:
  enable: false
  # 最新评论
  recentComment:
    enable: false
    # 缓存时间 1: 1天 / .5 : 半天
    storage: .2
  card:
    # 标签
    tags: true
    # 归档
    archive: "month" # month: 按月 / year: 按年
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 简繁体转换
translate:
  enable: true
  defaultEncoding: 2 # 1: 默认繁体 2: 默认简体
  translateDelay: 0 # 首次加载翻译迟疑时间
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 右下角悬停导航栏
rightside:
  enable: false
  percent: false
  hide:
    enable: false
    translate: false
    mode: false
    aside: false
# --------------------------- start ---------------------------

# --------------------------- start ---------------------------
# 页脚
footer:
  # 社交图标
  information:
    author: false # 图片链接 / false
    left:
      # Github: https://github.com/everfu || fab fa-github # 名称: 链接 || 图标
      # Mail: mailto:<EMAIL> || far fa-envelope
    right:
      # Bilibili: https://space.bilibili.com/1329819902 || fab fa-bilibili
      # Douyin: https://v.douyin.com/iJsLc8jt/ || fab fa-tiktok
  # 友情链接
  group:
    # 导航:
    #   Archives: /archives/
    #   Categories: /categories/
    #   Tags: /tags/
    # 排列:
    #   Cookies: /cookies/
    #   Privacy: /privacy/
    #   Copyright: /copyright/

  # 随机友链
  randomlink: false # 随机友链

  # 备案
  beian:
    # - name: 湘公网安备43048102000175号
    #   icon: https://beian.mps.gov.cn/img/logo01.dd7ff50e.png
    #   url: https://beian.mps.gov.cn/#/query/webSearch
    # - name: 湘ICP备**********号-1
    #   url: https://beian.miit.gov.cn/

  # 页脚信息文字
  links:
    # - name: RSS
    #   url: /atom.xml
    # - name: License
    #   url: https://github.com/everfu/hexo-theme-solitude/blob/main/LICENSE
    #   icon:
    #     - fas fa-copyright
    #     - fab fa-creative-commons-by
    #     - fab fa-creative-commons-nc
    #     - fab fa-creative-commons-nd
    # - name: boringbay
    #   url: https://boringbay.com/
    #   img: https://boringbay.com/api/badge/www.efu.me
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 404 页面
errorpage:
  img: /img/404.avif
  text: =awa= Page Not Found # 文字
  recommendList: true
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Meting API
# 这部分内容使用 metingjs，
# 只能使用中国大陆地区支持的网易云音乐、QQ音乐等音乐平台，
# 后续考虑使用 JSON 文件存储音乐信息并自定义实现第三方 API 不依赖页面。
# 音乐页面
meting_api: "https://meting.qjqq.cn/?server=:server&type=:type&id=:id&auth=:auth&r=:r" # 自定义 API
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 音乐胶囊
capsule:
  enable: false
  # 歌单 ID / 单曲 ID
  id: 5144842535
  # 服务商：netease / qq / xiami / kugou / baidu
  server: netease
  # 类型：playlist / song
  type: playlist
  volume: 0.8
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 快捷菜单
# 使用 shift + ? 打开
keyboard:
  enable: false
  list:
    # - name: 关闭快捷菜单
    #   key: K
    #   func: keyboard
    # - name: 打开控制台
    #   key: A
    #   sco: showConsole
    # - name: 播放/暂停音乐
    #   key: M
    #   sco: musicToggle
    # - name: 打开友链
    #   key: L
    #   url: /links/
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 图片懒加载
lazyload:
  enable: false
  # post, site
  field: site
  # 加载时替换图
  placeholder: ""
  # 加载失败替换图
  errorimg: /img/error_load.avif
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 加载
loading:
  # 全屏加载
  fullpage: false
  # 加载图标，不写默认siteicon
  favicon: /img/favicon.png
  # Pace 加载
  pace: true
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 代码块高亮
highlight:
  enable: true
  # 当超过多少字时显示折叠按钮
  limit: 200
  # 是否启用复制按钮
  copy: true
  # 是否默认展开
  expand: true
  # default: 默认 / mac : 苹果终端
  theme: mac
  # default / solidity / dracula
  color: default
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 图片灯箱
lightbox: false
# 警告: 请任选其一，但不能同时开启。
fancybox: false # fancybox
mediumZoom: false # mediumZoom
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 纪念日
# 在纪念日整个网站变灰
memorial:
  enable: false
  date:
  #  - 7-7
  #  - 9-18
  #  - 12-13
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# OpenGraph
OpenGraph:
  enable: false
  options:
    # twitter_card:
    # twitter_image:
    # twitter_id:
    # twitter_site:
    # google_plus:
    # fb_admins:
    # fb_app_id:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 字数统计
# 警告: 请先安装 hexo-wordcount 插件。
wordcount: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Katex
# Latex 公式支持
katex:
  enable: false
  # 是否在每个页面加载
  per_page: false
  # 是否启用复制公式
  copytex: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 验证
verify_site:
  # - name: google-site-verification
  #   content: xxxxxx
  # - name: baidu-site-verification
  #   content: xxxxxxx
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# CSS 前缀
# 开启后会自动给 CSS 加前缀（以获得更好的浏览器支持），但这会增加 CSS 文件的大小。
css_prefix: false
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 扩展
extends:
  # 插入到 head
  head:
    - <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    - <link rel="stylesheet" href="/css/custom.css">
    - <link rel="stylesheet" href="/css/clock-widget.css">
    - <link rel="stylesheet" href="/css/mobile-fix-gentle.css">  # 温和的移动端修复
    - <script src="/js/weather-manager.js" defer></script>
    - <script src="/js/clock-widget.js" defer></script>
    - <script src="/js/banner-clock.js" defer></script>
  #  - <script src="https://cdn.bootcdn.net/ajax/libs/pace/1.2.4/pace.min.js"></script>

  # 插入到 body
  body:
    # 手动位置选择弹窗
    - |
      <div id="location-selector-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10000; backdrop-filter: blur(5px);">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 12px; padding: 25px; max-width: 400px; width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
          <div style="text-align: center; margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">📍 选择您的位置</h3>
            <p style="margin: 0; color: #666; font-size: 14px;">为了提供准确的天气信息，请选择您的城市</p>
          </div>

          <div style="margin-bottom: 20px;">
            <input type="text" id="city-search-input" placeholder="搜索城市名称..."
                   style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px; box-sizing: border-box; outline: none; transition: border-color 0.3s;">
            <div id="city-suggestions" style="max-height: 200px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; margin-top: 10px; display: none;"></div>
          </div>

          <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #333; font-size: 14px;">🔥 热门城市</h4>
            <div id="popular-cities" style="display: flex; flex-wrap: wrap; gap: 8px;">
              <button class="city-btn" data-city="北京市">北京</button>
              <button class="city-btn" data-city="上海市">上海</button>
              <button class="city-btn" data-city="广州市">广州</button>
              <button class="city-btn" data-city="深圳市">深圳</button>
              <button class="city-btn" data-city="杭州市">杭州</button>
              <button class="city-btn" data-city="南京市">南京</button>
              <button class="city-btn" data-city="成都市">成都</button>
              <button class="city-btn" data-city="武汉市">武汉</button>
            </div>
          </div>

          <div style="display: flex; gap: 10px; justify-content: flex-end;">
            <button id="location-cancel-btn" style="padding: 10px 20px; border: 2px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer; font-size: 14px;">稍后设置</button>
            <button id="location-confirm-btn" style="padding: 10px 20px; border: none; background: #4CAF50; color: white; border-radius: 6px; cursor: pointer; font-size: 14px;" disabled>确认选择</button>
          </div>
        </div>
      </div>

      <style>
        .city-btn {
          padding: 8px 12px;
          border: 1px solid #ddd;
          background: #f9f9f9;
          color: #333;
          border-radius: 20px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.3s;
        }
        .city-btn:hover {
          background: #e3f2fd;
          border-color: #2196f3;
          color: #2196f3;
        }
        .city-btn.selected {
          background: #4CAF50;
          border-color: #4CAF50;
          color: white;
        }
        .city-suggestion {
          padding: 12px;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
          transition: background 0.2s;
        }
        .city-suggestion:hover {
          background: #f5f5f5;
        }
        .city-suggestion:last-child {
          border-bottom: none;
        }
        #city-search-input:focus {
          border-color: #4CAF50;
        }
      </style>
    - |
      <script>
      // 多重检查确保横幅时钟正确创建
      function initBannerClock() {
        console.log('🚀 开始初始化横幅时钟...');

        // 创建横幅时钟
        function createBannerClock() {
          const banners = document.getElementById('banners');
          if (!banners) {
            console.warn('⚠️ 找不到 banners 元素');
            return false;
          }

          if (document.getElementById('banner-clock')) {
            console.log('✅ banner-clock 已存在');
            return true;
          }

          const bannerClock = document.createElement('div');
          bannerClock.id = 'banner-clock';
          bannerClock.innerHTML = `
            <div class="banner-clock-time">加载中...</div>
            <div class="banner-clock-date">加载中...</div>
            <div class="banner-clock-weather">
              <div class="banner-clock-weather-item">
                <i class="fas fa-cloud-sun"></i>
                <span>获取天气中...</span>
              </div>
              <div class="banner-clock-weather-item">
                <i class="fas fa-tint"></i>
                <span>--</span>
              </div>
            </div>
            <div class="banner-clock-location">
              <i class="fas fa-map-marker-alt"></i>
              <span>获取位置中...</span>
            </div>
          `;
          banners.appendChild(bannerClock);
          console.log('✅ 横幅时钟 DOM 元素已创建');

          // 延迟初始化，给页面更多加载时间
          const initWidget = (attempt = 1, maxAttempts = 5) => {
            if (typeof BannerClockWidget !== 'undefined') {
              console.log(`🚀 初始化横幅时钟组件... (尝试 ${attempt})`);
              try {
                new BannerClockWidget();
                console.log('✅ 横幅时钟组件初始化成功');
              } catch (error) {
                console.error('❌ BannerClockWidget 初始化失败:', error);
                if (attempt < maxAttempts) {
                  console.log(`🔄 ${attempt * 1000}ms后重试...`);
                  setTimeout(() => initWidget(attempt + 1, maxAttempts), attempt * 1000);
                } else {
                  console.warn('⚠️ 横幅时钟组件初始化最终失败，使用备用时钟');
                  startBackupClock();
                }
              }
            } else {
              if (attempt < maxAttempts) {
                console.log(`⏳ BannerClockWidget 未就绪，${attempt * 500}ms后重试... (${attempt}/${maxAttempts})`);
                setTimeout(() => initWidget(attempt + 1, maxAttempts), attempt * 500);
              } else {
                console.warn('⚠️ BannerClockWidget 未定义，使用备用时钟');
                startBackupClock();
              }
            }
          };

          // 移动端延迟更长时间
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          const initialDelay = isMobile ? 2000 : 1000;

          setTimeout(() => initWidget(), initialDelay);

          // 备用时钟函数（增强版）
          function startBackupClock() {
            console.log('🔄 启动备用横幅时钟...');

            function updateBannerClock() {
              const timeEl = document.querySelector('#banner-clock .banner-clock-time');
              const dateEl = document.querySelector('#banner-clock .banner-clock-date');
              const locationEl = document.querySelector('#banner-clock .banner-clock-location span');

              if (timeEl && dateEl) {
                const now = new Date();
                // 手动格式化时间以确保正确的格式
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                timeEl.textContent = `${hours}:${minutes}:${seconds}`;

                // 格式化日期
                const year = now.getFullYear();
                const month = now.getMonth() + 1;
                const day = now.getDate();
                const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
                const weekday = weekdays[now.getDay()];
                dateEl.textContent = `${month}月${day}日${weekday}`;
              }
            }

            // 备用位置获取
            function tryGetLocationForBackup() {
              if (navigator.geolocation) {
                console.log('📍 备用时钟尝试获取位置...');

                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                const timeout = isMobile ? 25000 : 15000; // 移动端更长超时

                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    console.log('✅ 备用时钟位置获取成功');
                    // 这里可以调用逆地理编码，但为了简化，我们使用天气管理器的结果
                    if (window.weatherManager && window.weatherManager.city) {
                      const locationEl = document.querySelector('#banner-clock .banner-clock-location span');
                      if (locationEl) {
                        locationEl.textContent = window.weatherManager.city;
                      }
                    }
                  },
                  (error) => {
                    console.warn('⚠️ 备用时钟位置获取失败:', error.message);
                    const locationEl = document.querySelector('#banner-clock .banner-clock-location span');
                    if (locationEl) {
                      locationEl.textContent = '北京市';
                    }
                  },
                  {
                    timeout: timeout,
                    enableHighAccuracy: false,
                    maximumAge: 600000 // 10分钟缓存
                  }
                );
              }
            }

            updateBannerClock();
            setInterval(updateBannerClock, 1000);

            // 延迟获取位置，避免与主要位置获取冲突
            setTimeout(tryGetLocationForBackup, 5000);

            console.log('✅ 备用时钟已启动');
          }

          return true;
        }

        // 执行创建横幅时钟
        return createBannerClock();
      }

      // 多层级位置获取策略说明
      console.log('🌍 多层级位置获取策略:');
      console.log('  第一层: 无感IP定位');
      console.log('  第二层: GPS定位(带隐私检测)');
      console.log('  第三层: 智能推断(时区/语言)');
      console.log('  第四层: 手动选择位置');

      // 检测设备类型和隐私设置
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile) {
        console.log('📱 检测到移动设备，启用移动端优化策略');
      }

      // 检测隐私管家等软件
      function detectPrivacyProtection() {
        if (!navigator.geolocation) {
          console.log('🛡️ 地理位置API不可用');
          return true;
        }

        // 快速检测是否被拦截
        const startTime = Date.now();
        navigator.geolocation.getCurrentPosition(
          () => {},
          (error) => {
            const elapsed = Date.now() - startTime;
            if (error.code === error.PERMISSION_DENIED && elapsed < 50) {
              console.log('🛡️ 检测到隐私保护软件拦截');
            }
          },
          { timeout: 100 }
        );
      }

      detectPrivacyProtection();

      // 在 DOMContentLoaded 时尝试创建
      document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initBannerClock, 100);
      });

      // 在 window.load 时再次尝试创建
      window.addEventListener('load', function() {
        setTimeout(initBannerClock, 200);
      });

      // 延迟执行确保所有元素都已加载
      setTimeout(function() {
        initBannerClock();

        // 等待侧边栏加载完成
        setTimeout(function() {
          const asideContent = document.querySelector('.aside-content');
          const cardInfo = document.querySelector('.card-widget.card-info');
          if (asideContent && cardInfo) {
            // 创建时钟组件
            const clockWidget = document.createElement('div');
            clockWidget.className = 'card-widget card-clock';
            clockWidget.id = 'aside-clock';
            clockWidget.innerHTML = `
              <div class="card-content">
                <div class="clock-widget">
                  <div class="clock-header">
                    <span class="clock-date"></span>
                    <span class="clock-weather">
                      <i class="fas fa-cloud-sun"></i>
                      <span>多云 37°C</span>
                      <i class="fas fa-tint"></i>
                      <span>39%</span>
                    </span>
                  </div>
                  <div class="clock-time">16:46:15</div>
                  <div class="clock-location">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="clock-city">获取中...</span>
                  </div>
                </div>
              </div>
            `;
            // 在信息卡后面插入时钟组件
            cardInfo.parentNode.insertBefore(clockWidget, cardInfo.nextSibling);
            
            // 初始化时钟功能
            if (typeof ClockWidget !== 'undefined') {
              new ClockWidget();
            }
          }

          // 创建横幅时钟
          createBannerClock();

          // 初始化主题自动切换功能
          initAutoThemeSwitch();
        }, 1000);

        // 主题自动切换功能
        function initAutoThemeSwitch() {
          let lastThemeCheck = null;

          // 检查是否启用自动主题切换
          function isAutoThemeEnabled() {
            return localStorage.getItem('auto-theme-switch') === 'true';
          }

          // 自动主题切换逻辑
          function autoSwitchTheme() {
            if (!isAutoThemeEnabled()) {
              return;
            }

            const now = new Date();
            const hour = now.getHours();

            // 避免频繁检查，只在小时变化时检查
            if (lastThemeCheck === hour) {
              return;
            }

            lastThemeCheck = hour;

            // 获取当前主题状态
            const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

            // 6:00-18:00 使用浅色主题，18:00-6:00 使用深色主题
            const shouldBeDark = hour < 6 || hour >= 18;

            if (shouldBeDark && !isDarkMode) {
              switchToDarkMode();
              console.log('🌙 自动切换到深色主题');
            } else if (!shouldBeDark && isDarkMode) {
              switchToLightMode();
              console.log('☀️ 自动切换到浅色主题');
            }
          }

          // 切换到深色主题
          function switchToDarkMode() {
            try {
              if (typeof sco !== 'undefined' && sco.switchDarkMode) {
                const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                if (!isDark) {
                  sco.switchDarkMode();
                }
              } else {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
              }
            } catch (error) {
              console.warn('⚠️ 切换到深色主题失败:', error);
            }
          }

          // 切换到浅色主题
          function switchToLightMode() {
            try {
              if (typeof sco !== 'undefined' && sco.switchDarkMode) {
                const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                if (isDark) {
                  sco.switchDarkMode();
                }
              } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
              }
            } catch (error) {
              console.warn('⚠️ 切换到浅色主题失败:', error);
            }
          }

          // 启用自动主题切换（默认启用）
          if (localStorage.getItem('auto-theme-switch') === null) {
            localStorage.setItem('auto-theme-switch', 'true');
          }

          // 立即检查一次主题
          autoSwitchTheme();

          // 每分钟检查一次主题
          setInterval(autoSwitchTheme, 60000);
        }
      });
      
      // PJAX支持
      if (typeof pjax !== 'undefined') {
        document.addEventListener('pjax:complete', function() {
          setTimeout(function() {
            const asideContent = document.querySelector('.aside-content');
            const cardInfo = document.querySelector('.card-widget.card-info');
            const existingClock = document.querySelector('#aside-clock');
            
            if (asideContent && cardInfo && !existingClock) {
              const clockWidget = document.createElement('div');
              clockWidget.className = 'card-widget card-clock';
              clockWidget.id = 'aside-clock';
              clockWidget.innerHTML = `
                <div class="card-content">
                  <div class="clock-widget">
                    <div class="clock-header">
                      <span class="clock-date"></span>
                      <span class="clock-weather">
                        <i class="fas fa-cloud-sun"></i>
                        <span>多云 37°C</span>
                        <i class="fas fa-tint"></i>
                        <span>39%</span>
                      </span>
                    </div>
                    <div class="clock-time">16:46:15</div>
                    <div class="clock-location">
                      <i class="fas fa-map-marker-alt"></i>
                      <span class="clock-city">获取中...</span>
                    </div>
                  </div>
                </div>
              `;
              cardInfo.parentNode.insertBefore(clockWidget, cardInfo.nextSibling);
              
              if (typeof ClockWidget !== 'undefined') {
                new ClockWidget();
              }
            }

            // 重新创建横幅时钟
            createBannerClock();
          }, 500);
        });
      }
      </script>
  #  - <script src="https://cdn.bootcdn.net/ajax/libs/pace/1.2.4/pace.min.js"></script>
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# PWA
# 渐进式网络应用程序
pwa:
  enable: false
  manifest: /manifest.json # manifest.json
  theme_color: "#006a73" # 主题颜色
  mask_icon: /img/pwa/favicon.png # 遮罩图标
  apple_touch_icon: /img/pwa/favicon.png # Apple touch 图标
  bookmark_icon: /img/pwa/favicon.png # 书签图标
  favicon_32_32: /img/pwa/favicon_32.png # 32x32 图标
  favicon_16_16: /img/pwa/favicon_16.png # 16x16 图标
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 评论
comment:
  # 使用哪个评论系统（例如： waline or waline,twikoo）
  use: # waline, twikoo, valine, artalk, giscus # 最多可以同时开启两个评论系统
  # 是否显示热门评论开关
  commentBarrage: false
  # 懒加载评论区
  lazyload: false
  # 显示评论数
  count: false
  # Sidebar 显示总评论数
  sidebar: false
  # PV
  pv: false
  # 头像
  avatar: https://gravatar.com/avatar
  # 热评提示
  hot_tip:
    enable: false
    # 热评数量
    count: 3
  # 最新评论(⚠️ 需要先配置评论)
  newest_comment:
    enable: true
    storage: .5 # 缓存时间 1: 1天 / .5 : 半天
    limit: 5 # 评论数
# Twikoo: https://solitude.js.org/comment/twikoo
twikoo: # https://twikoo.js.org/
  envId: # url: https://twikoo.sondy.top/
  region: # 环境地域，默认为 ap-shanghai，腾讯云环境填 ap-shanghai 或 ap-guangzhou；Vercel 环境不填。
  style: true # 开启时使用自定义样式
  accessToken: # AccessToken
  option: # twikoo 选项
# Waline: https://solitude.js.org/comment/waline
waline: # https://waline.js.org/
  envId: # url: https://waline.wzsco.top
  pageview: false # 是否启用页面访问统计
  option: # waline 配置项
# Valine: https://solitude.js.org/comment/valine
valine:
  appId: # leancloud 应用 app id
  appKey: # leancloud 应用 app key
  serverURLs: # 此配置适用于国内自定义域名用户，海外版本会自动检测（无需手动填入）
  avatar: # https://valine.js.org/avatar.html
  visitor: false
  style: true # 开启时使用自定义样式
  option: # 选项列表
# Artalk: https://solitude.js.org/comment/artalk
# Artalk: https://solitude.js.org/zh/comment/artalk
artalk:
  server: # 服务器 url
  site: # 站点名称
  option: # 选项
# Giscus: https://solitude.js.org/comment/giscus
giscus:
  repo: # GitHub 仓库名
  repo_id: # GitHub 仓库 ID
  category_id: # GitHub 仓库分类 ID
  theme:
    light: light
    dark: dark
  option:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 搜索
search:
  enable: true
  type: local # local / algolia / docsearch
  tags:
    # - Solitude
    # - Hexo
  # Algolia
  algolia:
    # hits:
    #   per_page: 6

  # 本地搜索
  local:
    preload: false
    CDN: # url: search.xml

  # DocSearch
  # https://docsearch.algolia.com/
  docsearch:
    appId:
    apiKey:
    indexName:
    option:
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 音乐馆
music:
  enable: false
  # 歌单 ID / 单曲 ID
  id: 5144842535
  # 服务商：netease / qq / xiami / kugou / baidu
  server: netease
  # 类型：playlist / song
  type: playlist
  # 默认音量
  volume: 0.8
  # 是否自动播放
  mutex: true
  # 播放方式：list / random
  order: list
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 弹幕留言页面
envelope:
  enable: false
  line: 10 # 显示行数
  speed: 20 # 播放速度
  hover: true # 鼠标悬停暂停
  loop: true # 循环播放
  page: /message/ # 留言板页面
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 即可短文
brevity:
  enable: false
  home_mini: false
  music: false
  page: /essay/
  style: 1
  strip: 30
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 最近评论页面
recent_comments:
  enable: false
  limit: 50 # ⚠️waline 仅支持最大50条评论
  cache: 0.2 # 1 = 1天
  page: /recentcomments/ # 最近评论页面
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# Busuanzi
busuanzi: false
# 0: 原版 / 1: 自定义版
busuanzi_use: 0
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 谷歌广告
google_adsense:
  enable: false
  # 自动广告
  auto_ads: false
  # 页面级广告
  enable_page_level_ads: true
  # 侧边栏卡片广告
  aside_card: true
  # 文章卡片广告
  post_card: true
  # 文章内容广告
  post_content: true
  # 谷歌广告 js
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js
  client: # ca-pub-XXXXXXXXXXXXXX
  slot: # 4236388782
# --------------------------- end ---------------------------

# --------------------------- start ---------------------------
# 非必要请勿修改
CDN:
  internal: local # local / cdnjs / jsdelivr / unpkg / custom
  third_party: custom # cdnjs / jsdelivr / unpkg / custom
  version: true # 是否使用版本号
  custom_format: https://fastly.jsdelivr.net/npm/${name}@${version}/${min_file} # 自定义格式
  # 直接覆盖默认 CDN 链接（优先级最高）
  options:
    # algolia_search:
    # aplayer_css:
    # aplayer_js:
    # artalk_css:
    # artalk_js:
    # blueimp_md5:
    # busuanzi_js:
    # chart_js:
    # color_thief:
    # fancyapps_css:
    # fancyapps_ui:
    # fontawesome:
    # instantsearch:
    # katex:
    # katex_copytex:
    # lazyload:
    # medium_zoom:
    # mermaid_js:
    # meting_js:
    # pace_js:
    # pjax:
    # qrcode:
    # snackbar:
    # swiper_css:
    # swiper_js:
    # twikoo:
    # typeit_js:
    # valine:
    # waline_css:
    # waline_js:
    # 自定义时钟组件
    clock_widget_css: /css/clock-widget.css
    clock_widget_js: /js/clock-widget.js
# --------------------------- end ---------------------------